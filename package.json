{"name": "alx-course-tracker", "version": "0.1.0", "private": true, "dependencies": {"@radix-ui/react-slot": "^1.1.2", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cra-template": "1.2.0", "dotenv": "^16.5.0", "glob": "^11.0.1", "gray-matter": "^4.0.3", "lucide-react": "^0.475.0", "postcss-loader": "^8.1.1", "puppeteer": "^24.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.3", "react-router-dom": "^7.1.5", "react-scripts": "^5.0.1", "react-syntax-highlighter": "^15.6.1", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@tailwindcss/postcss7-compat": "^2.2.17", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "autoprefixer": "^9", "postcss": "^7", "tailwindcss": "^4.0.6", "typescript": "^5.7.3"}, "overrides": {"tailwindcss": "^4.0.6"}}